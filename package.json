{"name": "submission", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@eslint/js": "^9.27.0", "eslint": "^9.27.0", "eslint-config-dicodingacademy": "^0.9.4", "globals": "^16.2.0"}, "dependencies": {"@hapi/hapi": "^21.4.0", "nanoid": "^3.3.11"}}