const {
  add<PERSON><PERSON><PERSON><PERSON><PERSON>,
  get<PERSON>ll<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  getBookByIdHand<PERSON>,
  editBookByIdHandler,
  deleteBookByIdHandler,
} = require('./bookshandler');

const routes = [
  {
    method: 'POST',
    path: '/books',
    handler: addB<PERSON><PERSON>and<PERSON>,
  },
  {
    method: 'GET',
    path: '/books',
    handler: getAll<PERSON>ooksHandler,
  },
  {
    method: 'GET',
    path: '/books/{bookId}',
    handler: getBookByIdHandler,
  },
  {
    method: 'PUT',
    path: '/books/{bookId}',
    handler: editBookByIdHandler,
  },
  {
    method: 'DELETE',
    path: '/books/{bookId}',
    handler: deleteBook<PERSON>yIdHandler,
  },
];

module.exports = routes;
